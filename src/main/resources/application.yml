server:
  port: 8080

spring:
  servlet:
    multipart:
      enabled: true
      max-file-size: 100MB
      max-request-size: 100MB
  datasource:
    url: ******************************************************************************************************
    driverClassName: com.mysql.cj.jdbc.Driver
    username: root
    password: root
  application:
    name: radio-rangrez
  jpa:
    generate-ddl: true
    hibernate:
      ddl-auto: update
      globally-quoted-identifiers: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect

#---------------------------------jwt-------------------------------------------------#
jwt:
  secret: "f29jdmkPz&7sB9cnL2AjQ#pFvZtM6qW8"  # 32+ chars strong secret
  expiration: 86400000
  token:
    secret: "N7d$WqPx!zLmRvYc8BtK#rXpZoMjQ2e1"  # Also 32+ chars

#---------------------------------AWS Cognito-------------------------------------------------#
aws:
  cognito:
    region: eu-north-1  # Change to your AWS region
    access-key: ********************  # Replace with your AWS access key
    secret-key: IY8zq+X0dOh18ZpFm38Y2LjWYB2ulh4O78VIqScg  # Replace with your AWS secret key
    user-pool-id: eu-north-1_G4vpcRRYQ  # Replace with your Cognito User Pool ID
    client-id: 4c2cujemsd4qns01la29v3j6v3  # Replace with your Cognito App Client ID
    client-secret: 15f84g3e5euspg49hgt1j645n2dhejh8596nk2fkknkag1taqdj9

  #---------------------------------AWS S3-------------------------------------------------#
  s3:
    region: eu-north-1  # Change to your AWS region
    access-key: ********************  # Replace with your AWS access key (can be same as Cognito)
    secret-key: IY8zq+X0dOh18ZpFm38Y2LjWYB2ulh4O78VIqScg  # Replace with your AWS secret key (can be same as Cognito)
    bucket-name: radio-rangrez  # Replace with your S3 bucket name
    base-url: https://radio-rangrez.s3.eu-north-1.amazonaws.com    # Replace with your bucket URL
    presigned-url-expiration-minutes: 15  # Presigned URL expiration time

#---------------------------------rate limit-------------------------------------------------#
rate:
  limit:
    enabled: false
    request:
      time: 1
      limit: 3

#-----------------------verification expiry time minute--------------------------------#
verification:
  expiry:
    in:
      minute: 10080

#---------------------------------Google Calendar API-------------------------------------------------#
google:
  calendar:
    enabled: false  # Set to true when you have valid Google Calendar credentials
    application-name: Radio Rangrez Event Management
    credentials-file-path: credentials.json  # Path to your Google OAuth2 credentials file
    tokens-directory-path: tokens  # Directory to store access tokens
    scopes:
      - https://www.googleapis.com/auth/calendar
      - https://www.googleapis.com/auth/calendar.events
    redirect-uri: http://localhost:8080/api/v1/events/oauth/callback

#---------------------------------OpenAI API-------------------------------------------------#
openai:
  api-key: ${OPENAI_API_KEY:your-openai-api-key-here}  # Set via environment variable
  model: gpt-3.5-turbo  # Model to use for playlist generation
  max-tokens: 500  # Maximum tokens for response
  temperature: 0.3  # Lower temperature for more consistent results
