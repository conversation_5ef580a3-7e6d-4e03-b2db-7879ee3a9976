package com.radio.rangrez.dto.playlist;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.radio.rangrez.dto.SongDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PlaylistDto {
    
    private Long id;
    private String name;
    private String description;
    private String userQuery;
    private List<SongDto> tracks;
    private Integer totalDurationSeconds;
    private Integer totalTracks;
    private LocalDateTime created;
    private LocalDateTime updated;
    private boolean activated;
}
