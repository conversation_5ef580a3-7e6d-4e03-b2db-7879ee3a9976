package com.radio.rangrez.repository;

import com.radio.rangrez.model.Playlist;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface PlaylistRepository extends JpaRepository<Playlist, Long> {
    
    List<Playlist> findByUserIdAndActivatedTrueOrderByCreatedDesc(Long userId);
    
    List<Playlist> findByActivatedTrueOrderByCreatedDesc();
    
    @Query("SELECT p FROM Playlist p WHERE p.activated = true AND p.name LIKE %:name%")
    List<Playlist> findByNameContainingIgnoreCaseAndActivatedTrue(@Param("name") String name);
}
