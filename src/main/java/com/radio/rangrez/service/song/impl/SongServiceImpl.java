package com.radio.rangrez.service.song.impl;

import com.radio.rangrez.dto.*;
import com.radio.rangrez.model.*;
import com.radio.rangrez.repository.*;
import com.radio.rangrez.service.song.SongService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class SongServiceImpl implements SongService {

    @Autowired
    private SongRepository songRepository;

    @Autowired
    private SongArtistRepository songArtistRepository;

    @Autowired
    private SongGenreRepository songGenreRepository;

    @Autowired
    private SongMoodRepository songMoodRepository;

    @Autowired
    private ArtistRepository artistRepository;

    @Autowired
    private GenreRepository genreRepository;

    @Autowired
    private MoodRepository moodRepository;

    @Override
    @Transactional
    public SongDto createSong(CreateSongRequest request) {
        Song song = new Song();
        BeanUtils.copyProperties(request, song, "id", "created", "updated", "artists", "genreIds", "moodIds");

        Song savedSong = songRepository.save(song);

        // Add artists
        if (request.getArtists() != null && !request.getArtists().isEmpty()) {
            for (CreateSongRequest.SongArtistRequest artistRequest : request.getArtists()) {
                Optional<Artist> artistOptional = artistRepository.findById(artistRequest.getArtistId());
                if (artistOptional.isPresent()) {
                    SongArtist songArtist = new SongArtist();
                    songArtist.setSong(savedSong);
                    songArtist.setArtist(artistOptional.get());
                    songArtist.setRole(artistRequest.getRole());
                    songArtistRepository.save(songArtist);
                }
            }
        }

        // Add genres
        if (request.getGenreIds() != null && !request.getGenreIds().isEmpty()) {
            for (Long genreId : request.getGenreIds()) {
                Optional<Genre> genreOptional = genreRepository.findById(genreId);
                if (genreOptional.isPresent()) {
                    SongGenre songGenre = new SongGenre();
                    songGenre.setSong(savedSong);
                    songGenre.setGenre(genreOptional.get());
                    songGenreRepository.save(songGenre);
                }
            }
        }

        // Add moods
        if (request.getMoodIds() != null && !request.getMoodIds().isEmpty()) {
            for (Long moodId : request.getMoodIds()) {
                Optional<Mood> moodOptional = moodRepository.findById(moodId);
                if (moodOptional.isPresent()) {
                    SongMood songMood = new SongMood();
                    songMood.setSong(savedSong);
                    songMood.setMood(moodOptional.get());
                    songMoodRepository.save(songMood);
                }
            }
        }

        return convertToDto(savedSong);
    }

    @Override
    public List<SongDto> getAllSongs() {
        List<Song> songs = songRepository.findAll();
        return songs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public Page<SongDto> getAllSongs(Pageable pageable) {
        Page<Song> songs = songRepository.findByActivatedTrue(pageable);
        return songs.map(this::convertToDto);
    }

    @Override
    public List<SongDto> getActiveSongs() {
        List<Song> songs = songRepository.findByActivatedTrueOrderByCreatedDesc();
        return songs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public SongDto getSongById(Long id) {
        Optional<Song> songOptional = songRepository.findById(id);
        if (songOptional.isPresent()) {
            return convertToDto(songOptional.get());
        }
        throw new RuntimeException("Song not found with id: " + id);
    }

    @Override
    @Transactional
    public SongDto updateSong(Long id, CreateSongRequest request) {
        Optional<Song> songOptional = songRepository.findById(id);
        if (songOptional.isPresent()) {
            Song existingSong = songOptional.get();

            // Update basic song properties
            copyNonNullProperties(request, existingSong);

            // Update artists if provided
            if (request.getArtists() != null) {
                songArtistRepository.deleteBySongId(id);
                for (CreateSongRequest.SongArtistRequest artistRequest : request.getArtists()) {
                    Optional<Artist> artistOptional = artistRepository.findById(artistRequest.getArtistId());
                    if (artistOptional.isPresent()) {
                        SongArtist songArtist = new SongArtist();
                        songArtist.setSong(existingSong);
                        songArtist.setArtist(artistOptional.get());
                        songArtist.setRole(artistRequest.getRole());
                        songArtistRepository.save(songArtist);
                    }
                }
            }

            // Update genres if provided
            if (request.getGenreIds() != null) {
                songGenreRepository.deleteBySongId(id);
                for (Long genreId : request.getGenreIds()) {
                    Optional<Genre> genreOptional = genreRepository.findById(genreId);
                    if (genreOptional.isPresent()) {
                        SongGenre songGenre = new SongGenre();
                        songGenre.setSong(existingSong);
                        songGenre.setGenre(genreOptional.get());
                        songGenreRepository.save(songGenre);
                    }
                }
            }

            // Update moods if provided
            if (request.getMoodIds() != null) {
                songMoodRepository.deleteBySongId(id);
                for (Long moodId : request.getMoodIds()) {
                    Optional<Mood> moodOptional = moodRepository.findById(moodId);
                    if (moodOptional.isPresent()) {
                        SongMood songMood = new SongMood();
                        songMood.setSong(existingSong);
                        songMood.setMood(moodOptional.get());
                        songMoodRepository.save(songMood);
                    }
                }
            }

            Song updatedSong = songRepository.save(existingSong);
            return convertToDto(updatedSong);
        }
        throw new RuntimeException("Song not found with id: " + id);
    }

    @Override
    @Transactional
    public SongDto updateSongGenres(Long id, UpdateSongGenresRequest request) {
        Optional<Song> songOptional = songRepository.findById(id);
        if (!songOptional.isPresent()) {
            throw new RuntimeException("Song not found with id: " + id);
        }

        Song existingSong = songOptional.get();

        // Remove existing genres
        songGenreRepository.deleteBySongId(id);

        // Add new genres
        for (Long genreId : request.getGenreIds()) {
            Optional<Genre> genreOptional = genreRepository.findById(genreId);
            if (genreOptional.isPresent()) {
                SongGenre songGenre = new SongGenre();
                songGenre.setSong(existingSong);
                songGenre.setGenre(genreOptional.get());
                songGenreRepository.save(songGenre);
            } else {
                throw new RuntimeException("Genre not found with id: " + genreId);
            }
        }

        return convertToDto(existingSong);
    }

    @Override
    @Transactional
    public SongDto updateSongArtists(Long id, UpdateSongArtistsRequest request) {
        Optional<Song> songOptional = songRepository.findById(id);
        if (!songOptional.isPresent()) {
            throw new RuntimeException("Song not found with id: " + id);
        }

        Song existingSong = songOptional.get();

        // Remove existing artists
        songArtistRepository.deleteBySongId(id);

        // Add new artists
        for (UpdateSongArtistsRequest.SongArtistRequest artistRequest : request.getArtists()) {
            Optional<Artist> artistOptional = artistRepository.findById(artistRequest.getArtistId());
            if (artistOptional.isPresent()) {
                SongArtist songArtist = new SongArtist();
                songArtist.setSong(existingSong);
                songArtist.setArtist(artistOptional.get());
                songArtist.setRole(artistRequest.getRole());
                songArtistRepository.save(songArtist);
            } else {
                throw new RuntimeException("Artist not found with id: " + artistRequest.getArtistId());
            }
        }

        return convertToDto(existingSong);
    }

    @Override
    public boolean deleteSong(Long id) {
        Optional<Song> songOptional = songRepository.findById(id);
        if (songOptional.isPresent()) {
            songRepository.deleteById(id);
            return true;
        }
        throw new RuntimeException("Song not found with id: " + id);
    }

    @Override
    public List<SongDto> searchSongsByTitle(String title) {
        List<Song> songs = songRepository.findByTitleContainingIgnoreCaseAndActivatedTrue(title);
        return songs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<SongDto> getSongsByAlbum(String albumName) {
        List<Song> songs = songRepository.findByAlbumNameContainingIgnoreCaseAndActivatedTrue(albumName);
        return songs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<SongDto> getSongsByLanguage(String language) {
        List<Song> songs = songRepository.findByLanguageContainingIgnoreCaseAndActivatedTrue(language);
        return songs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<SongDto> getFeaturedSongs() {
        List<Song> songs = songRepository.findByIsFeaturedTrueAndActivatedTrueOrderByPlayCountDesc();
        return songs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<SongDto> getPopularSongs() {
        List<Song> songs = songRepository.findTop10ByActivatedTrueOrderByPlayCountDesc();
        return songs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<SongDto> getTopRatedSongs() {
        List<Song> songs = songRepository.findTop10ByActivatedTrueOrderByAverageRatingDesc();
        return songs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<SongDto> getSongsByArtist(Long artistId) {
        List<Song> songs = songArtistRepository.findSongsByArtistId(artistId);
        return songs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<SongDto> getSongsByGenre(Long genreId) {
        List<Song> songs = songGenreRepository.findSongsByGenreId(genreId);
        return songs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public List<SongDto> getSongsByMood(Long moodId) {
        List<Song> songs = songMoodRepository.findSongsByMoodId(moodId);
        return songs.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    @Override
    public SongDto incrementPlayCount(Long songId) {
        Optional<Song> songOptional = songRepository.findById(songId);
        if (songOptional.isPresent()) {
            Song song = songOptional.get();
            song.setPlayCount(song.getPlayCount() + 1);
            Song updatedSong = songRepository.save(song);
            return convertToDto(updatedSong);
        }
        throw new RuntimeException("Song not found with id: " + songId);
    }

    @Override
    public SongDto rateSong(Long songId, Double rating) {
        if (rating < 1.0 || rating > 5.0) {
            throw new RuntimeException("Rating must be between 1.0 and 5.0");
        }

        Optional<Song> songOptional = songRepository.findById(songId);
        if (songOptional.isPresent()) {
            Song song = songOptional.get();
            
            // Calculate new average rating
            Long totalRatings = song.getTotalRatings();
            Double currentAverage = song.getAverageRating();
            Double newAverage = ((currentAverage * totalRatings) + rating) / (totalRatings + 1);
            
            song.setAverageRating(newAverage);
            song.setTotalRatings(totalRatings + 1);
            
            Song updatedSong = songRepository.save(song);
            return convertToDto(updatedSong);
        }
        throw new RuntimeException("Song not found with id: " + songId);
    }

    private SongDto convertToDto(Song song) {
        SongDto dto = new SongDto();
        BeanUtils.copyProperties(song, dto, "artists", "genres", "moods");

        // Load and convert artists
        List<SongArtist> songArtists = songArtistRepository.findBySongId(song.getId());
        List<SongArtistDto> artistDtos = songArtists.stream()
                .map(this::convertSongArtistToDto)
                .collect(Collectors.toList());
        dto.setArtists(artistDtos);

        // Load and convert genres
        List<Genre> genres = songGenreRepository.findGenresBySongId(song.getId());
        List<GenreDto> genreDtos = genres.stream()
                .map(this::convertGenreToDto)
                .collect(Collectors.toList());
        dto.setGenres(genreDtos);

        // Load and convert moods
        List<Mood> moods = songMoodRepository.findMoodsBySongId(song.getId());
        List<MoodDto> moodDtos = moods.stream()
                .map(this::convertMoodToDto)
                .collect(Collectors.toList());
        dto.setMoods(moodDtos);

        return dto;
    }

    private SongArtistDto convertSongArtistToDto(SongArtist songArtist) {
        SongArtistDto dto = new SongArtistDto();
        dto.setId(songArtist.getId());
        dto.setRole(songArtist.getRole());

        // Convert artist (without genres to avoid circular reference)
        ArtistDto artistDto = new ArtistDto();
        BeanUtils.copyProperties(songArtist.getArtist(), artistDto, "genres");
        dto.setArtist(artistDto);

        return dto;
    }

    private GenreDto convertGenreToDto(Genre genre) {
        GenreDto dto = new GenreDto();
        BeanUtils.copyProperties(genre, dto);
        return dto;
    }

    private MoodDto convertMoodToDto(Mood mood) {
        MoodDto dto = new MoodDto();
        BeanUtils.copyProperties(mood, dto);
        return dto;
    }

    private void copyNonNullProperties(CreateSongRequest source, Song target) {
        if (source.getTitle() != null) {
            target.setTitle(source.getTitle());
        }
        if (source.getDurationSeconds() != null) {
            target.setDurationSeconds(source.getDurationSeconds());
        }
        if (source.getAudioFileUrl() != null) {
            target.setAudioFileUrl(source.getAudioFileUrl());
        }
        if (source.getCoverImageUrl() != null) {
            target.setCoverImageUrl(source.getCoverImageUrl());
        }
        if (source.getAlbumName() != null) {
            target.setAlbumName(source.getAlbumName());
        }
        if (source.getTrackNumber() != null) {
            target.setTrackNumber(source.getTrackNumber());
        }
        if (source.getReleaseDate() != null) {
            target.setReleaseDate(source.getReleaseDate());
        }
        if (source.getLyrics() != null) {
            target.setLyrics(source.getLyrics());
        }
        if (source.getLanguage() != null) {
            target.setLanguage(source.getLanguage());
        }
        target.setExplicit(source.isExplicit());
        target.setFeatured(source.isFeatured());
    }
}